/* User Details Container */
.user-details-container {
  padding: var(--spacing-lg);
  background: var(--bg-main-content); /* Light grayish-white background */
  min-height: calc(100vh - 60px);
  max-width: 1200px;
  margin: 0 auto;
}

/* Header Section */
.header-section {
  margin-bottom: var(--spacing-lg);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-md);
}

.header-navigation {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
}

.back-btn {
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);

  &:hover {
    color: var(--text-primary);
    background: var(--bg-hover);
  }
}

.header-text {
  flex: 1;
}

.page-title {
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.edit-btn {
  height: 44px;
  border-radius: var(--radius-md);
  font-weight: 500;
  text-transform: none;

  mat-icon {
    margin-right: var(--spacing-xs);
  }
}

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  gap: var(--spacing-md);
}

.loading-text {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

/* Profile Section */
.profile-section {
  margin-bottom: var(--spacing-lg);
}

.profile-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.profile-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
}

.profile-picture {
  flex-shrink: 0;
}

.profile-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--border-primary);
}

.profile-placeholder {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: var(--bg-secondary);
  border: 3px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.initials {
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.basic-info {
  flex: 1;
}

.user-name {
  color: var(--text-primary);
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
}

.user-email {
  color: var(--text-secondary);
  font-size: 1.125rem;
  margin: 0 0 var(--spacing-md) 0;
}

.status-badge {
  display: flex;
  align-items: center;
}

:host ::ng-deep .status-active {
  background: #e8f5e8;
  color: #2e7d32;

  mat-icon {
    color: #2e7d32;
  }
}

:host ::ng-deep .status-inactive {
  background: #ffebee;
  color: #c62828;

  mat-icon {
    color: #c62828;
  }
}

/* Details Grid */
.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.info-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

:host ::ng-deep .info-card {
  .mat-mdc-card-header {
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md)
      var(--spacing-lg);
  }

  .mat-mdc-card-content {
    padding: 0 var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
  }

  .mat-mdc-card-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;

    mat-icon {
      color: var(--text-secondary);
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }
  }
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-secondary);

  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.875rem;
}

.info-value {
  color: var(--text-primary);
  font-size: 0.875rem;
  text-align: right;
  word-break: break-word;
}

.roles-container {
  padding: var(--spacing-sm) 0;
}

:host ::ng-deep .roles-container {
  .mat-mdc-chip {
    margin: var(--spacing-xs);
    font-size: 0.875rem;
    font-weight: 500;
  }

  .role-user {
    background: #e3f2fd;
    color: #1565c0;
  }

  .role-admin {
    background: #fff3e0;
    color: #ef6c00;
  }

  .role-super-admin {
    background: #fce4ec;
    color: #ad1457;
  }
}

.description-text {
  color: var(--text-primary);
  font-size: 0.875rem;
  line-height: 1.6;
  margin: 0;
  padding: var(--spacing-sm) 0;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  gap: var(--spacing-md);
}

.error-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  color: var(--text-muted);
}

.error-state h3 {
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.error-state p {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-details-container {
    padding: var(--spacing-md);
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .header-navigation {
    align-items: center;
  }

  .header-actions {
    justify-content: stretch;
  }

  .edit-btn {
    width: 100%;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .profile-image,
  .profile-placeholder {
    width: 100px;
    height: 100px;
  }

  .initials {
    font-size: 2rem;
  }

  .user-name {
    font-size: 1.5rem;
  }

  .details-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .info-value {
    text-align: left;
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .user-details-container {
    background: var(--bg-main-content); /* Light grayish-white background */
  }

  .profile-card,
  .info-card {
    background: #ffffff; /* White cards */
    border-color: #e0e0e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .page-title {
    color: #333333;
  }

  .page-subtitle {
    color: #666666;
  }

  .back-btn {
    color: #666666;

    &:hover {
      color: #333333;
      background: #f9f9f9;
    }
  }

  .user-name {
    color: #333333;
  }

  .user-email {
    color: #666666;
  }

  .profile-placeholder {
    background: #f5f5f5;
    border-color: #e0e0e0;
  }

  .initials {
    color: #333333;
  }

  .profile-image {
    border-color: #e0e0e0;
  }

  .info-label {
    color: #666666;
  }

  .info-value {
    color: #333333;
  }

  .description-text {
    color: #333333;
  }

  .loading-text {
    color: #666666;
  }

  .error-icon {
    color: #999999;
  }

  .error-state h3 {
    color: #333333;
  }

  .error-state p {
    color: #666666;
  }

  .info-row {
    border-bottom-color: #f0f0f0;
  }

  :host ::ng-deep .info-card {
    .mat-mdc-card-title {
      color: #333333;

      mat-icon {
        color: #666666;
      }
    }
  }

  :host ::ng-deep .status-active {
    background: #e8f5e8;
    color: #2e7d32;
  }

  :host ::ng-deep .status-inactive {
    background: #ffebee;
    color: #c62828;
  }
}
