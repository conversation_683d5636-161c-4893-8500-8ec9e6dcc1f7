/* Add/Edit User Container */
.add-edit-user-container {
  padding: var(--spacing-lg);
  background: var(--bg-main-content); /* Light grayish-white background */
  min-height: 100vh;
}

/* Header Section */
.header-section {
  margin-bottom: var(--spacing-lg);
}

.header-content {
  max-width: 800px;
}

.page-title {
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

/* Form Section */
.form-section {
  max-width: 800px;
}

.form-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-xl);
}

/* Section Titles */
.section-title {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-md) 0;
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-secondary);
}

/* Form Section Groups */
.form-section-group {
  margin-bottom: var(--spacing-xl);

  &:last-child {
    margin-bottom: var(--spacing-lg);
  }
}

/* Profile Image Section */
.profile-image-section {
  margin-bottom: var(--spacing-xl);
}

.image-upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border: 2px dashed var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
}

.image-preview {
  position: relative;
  display: inline-block;
}

.preview-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--border-primary);
}

.remove-image-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--error);
  color: white;
  width: 32px;
  height: 32px;

  &:hover {
    background: #d32f2f;
  }
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-muted);
}

.upload-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  color: var(--text-muted);
}

.upload-text {
  margin: 0;
  font-size: 0.875rem;
}

.upload-btn {
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  background: var(--bg-card);

  &:hover {
    background: var(--bg-hover);
    border-color: var(--border-secondary);
  }

  mat-icon {
    margin-right: var(--spacing-xs);
  }
}

/* Form Rows and Fields */
.form-row {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);

  &:last-child {
    margin-bottom: 0;
  }
}

:host ::ng-deep .form-field {
  flex: 1;

  &.full-width {
    width: 100%;
  }

  .mat-mdc-form-field-wrapper {
    padding-bottom: 0;
  }

  .mat-mdc-form-field-outline {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: var(--border-primary);
      border-width: 1px;
    }
  }

  &.mat-focused .mat-mdc-form-field-outline {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: var(--accent-green);
      border-width: 2px;
    }
  }

  &:hover:not(.mat-focused) .mat-mdc-form-field-outline {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: var(--border-secondary);
    }
  }

  .mat-mdc-form-field-label {
    color: var(--text-secondary);
  }

  .mat-mdc-input-element,
  .mat-mdc-select-trigger {
    color: var(--text-primary);
  }

  .mat-mdc-select-arrow {
    color: var(--text-muted);
  }

  // Error styling
  &.mat-form-field-invalid {
    .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: var(--error);
      }
    }
  }

  .mat-mdc-form-field-error {
    color: var(--error);
    font-size: 0.75rem;
  }
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-secondary);
  margin-top: var(--spacing-lg);
}

.cancel-btn {
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  background: var(--bg-card);

  &:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
    border-color: var(--border-secondary);
  }
}

.submit-btn {
  min-width: 140px;
  height: 44px;
  border-radius: var(--radius-md);
  font-weight: 500;
  text-transform: none;

  mat-icon {
    margin-right: var(--spacing-xs);
  }

  mat-spinner {
    margin-right: var(--spacing-xs);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .add-edit-user-container {
    padding: var(--spacing-md);
  }

  .form-card {
    padding: var(--spacing-lg);
  }

  .page-title {
    font-size: 1.5rem;
  }

  .form-row {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: var(--spacing-sm);
  }

  .cancel-btn,
  .submit-btn {
    width: 100%;
  }

  .image-upload-container {
    padding: var(--spacing-md);
  }

  .preview-image {
    width: 100px;
    height: 100px;
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .add-edit-user-container {
    background: var(--bg-main-content); /* Light grayish-white background */
  }

  .form-card {
    background: #ffffff; /* White card */
    border-color: #e0e0e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .section-title {
    color: #333333;
    border-bottom-color: #f0f0f0;
  }

  .page-title {
    color: #333333;
  }

  .page-subtitle {
    color: #666666;
  }

  .image-upload-container {
    background: #f9f9f9;
    border-color: #e0e0e0;
  }

  .upload-placeholder {
    color: #999999;
  }

  .upload-icon {
    color: #999999;
  }

  .upload-btn {
    color: #333333;
    background: #ffffff;
    border-color: #e0e0e0;

    &:hover {
      background: #f9f9f9;
      border-color: #d0d0d0;
    }
  }

  .cancel-btn {
    color: #666666;
    background: #ffffff;
    border-color: #e0e0e0;

    &:hover {
      background: #f9f9f9;
      color: #333333;
      border-color: #d0d0d0;
    }
  }

  .preview-image {
    border-color: #e0e0e0;
  }

  /* Form field styling for light theme */
  :host ::ng-deep .form-field {
    .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #e0e0e0;
      }
    }

    &.mat-focused .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #10b981;
        border-width: 2px;
      }
    }

    &:hover:not(.mat-focused) .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #d0d0d0;
      }
    }

    .mat-mdc-form-field-label {
      color: #666666;
    }

    .mat-mdc-input-element,
    .mat-mdc-select-trigger {
      color: #333333;
    }

    .mat-mdc-select-arrow {
      color: #999999;
    }

    &.mat-form-field-invalid {
      .mat-mdc-form-field-outline {
        .mdc-notched-outline__leading,
        .mdc-notched-outline__notch,
        .mdc-notched-outline__trailing {
          border-color: #f44336;
        }
      }
    }

    .mat-mdc-form-field-error {
      color: #f44336;
    }
  }
}
