/* Loading State */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--bg-primary);
}

.loading-content {
  text-align: center;
  color: var(--text-secondary);
}

.loading-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
  color: var(--accent-green);
  margin-bottom: var(--spacing-md);
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

/* Authentication Layout */
.auth-layout {
  width: 100%;
  height: 100vh;
  background: var(--bg-primary);
}

/* Main Layout (Authenticated) */
.main-layout {
  display: flex;
  height: 100vh;
  background: whitesmoke;
  overflow: hidden;
}

.sidebar {
  flex-shrink: 0;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(
    --bg-main-content
  ); /* Uses CSS variable for theme consistency */
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-layout {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  .sidebar {
    width: 100%;
    height: auto;
  }

  .main-content {
    flex: 1;
    min-height: calc(100vh - 200px);
  }

  .loading-icon {
    font-size: 2.5rem;
    width: 2.5rem;
    height: 2.5rem;
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .loading-container,
  .auth-layout {
    background: var(
      --bg-main-content
    ); /* Light grayish-white for refined design */
  }

  .main-layout {
    background: var(
      --bg-main-content
    ); /* Light grayish-white for main layout */
  }

  .main-content {
    background: var(
      --bg-main-content
    ); /* Light grayish-white for main content area */
  }

  .loading-text {
    color: #666666;
  }
}
