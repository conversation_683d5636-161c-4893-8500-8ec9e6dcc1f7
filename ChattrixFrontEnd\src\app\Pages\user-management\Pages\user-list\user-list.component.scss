/* User Management Container */
.user-management-container {
  padding: var(--spacing-lg);
  background: var(
    --bg-main-content
  ); /* Uses CSS variable for theme consistency */
  min-height: 100vh;
}

/* Header Section */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
  gap: var(--spacing-md);
}

.header-content {
  flex: 1;
}

.page-title {
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
}

.page-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.add-user-btn {
  height: 44px;
  border-radius: var(--radius-md);
  font-weight: 500;
  text-transform: none;

  mat-icon {
    margin-right: var(--spacing-xs);
  }
}

/* Table Header with Integrated Filters */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-card);
}

.table-header-left {
  display: flex;
  align-items: center;
}

.table-view-controls {
  display: flex;
  gap: var(--spacing-xs);

  .view-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    font-size: 0.875rem;
    text-transform: none;
    min-width: auto;

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    &.active {
      background: var(--bg-secondary);
      color: var(--text-primary);
    }

    &:hover:not(.active) {
      background: var(--bg-hover);
      color: var(--text-primary);
    }
  }
}

.table-header-right {
  display: flex;
  align-items: center;
}

.filters-container {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

:host ::ng-deep .search-field {
  width: 240px;

  .mat-mdc-form-field-wrapper {
    padding-bottom: 0;
  }

  .mat-mdc-form-field-outline {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: var(--border-primary);
      border-width: 1px;
    }
  }

  .mat-mdc-input-element {
    color: var(--text-primary);
    font-size: 0.875rem;

    &::placeholder {
      color: var(--text-muted);
    }
  }

  .mat-mdc-form-field-icon-suffix {
    color: var(--text-muted);
  }
}

:host ::ng-deep .filter-field {
  width: 120px;

  .mat-mdc-form-field-wrapper {
    padding-bottom: 0;
  }

  .mat-mdc-form-field-outline {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: var(--border-primary);
      border-width: 1px;
    }
  }

  .mat-mdc-form-field-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
  }

  .mat-mdc-select-trigger {
    color: var(--text-primary);
    font-size: 0.875rem;
  }

  .mat-mdc-select-arrow {
    color: var(--text-muted);
  }
}

.reset-btn {
  height: 40px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  background: var(--bg-card);
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: none;
  padding: 0 var(--spacing-md);

  &:hover {
    background: var(--bg-hover);
    border-color: var(--border-secondary);
  }
}

/* Table Section */
.table-section {
  margin-bottom: var(--spacing-lg);
}

.table-card {
  background: var(--bg-card);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  gap: var(--spacing-md);
}

.loading-text {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

.table-container {
  overflow-x: auto;
}

.users-table {
  width: 100%;
  background: var(--bg-card);

  .mat-mdc-header-cell {
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
    border-bottom: 1px solid var(--border-primary);
  }

  .mat-mdc-cell {
    border-bottom: 1px solid var(--border-secondary);
    color: var(--text-primary);
    font-size: 0.875rem;
  }

  .mat-mdc-row {
    &:hover {
      background: var(--bg-hover);
    }
  }
}

/* User Info Column */
.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--border-primary);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.user-avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #000000; /* Black background for minimalist design */
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1rem;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 500;
  color: var(--text-primary);
}

.user-email {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Roles Column */
.roles-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.role-chip {
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: var(--radius-sm);

  &.role-admin {
    background: #e3f2fd;
    color: #1976d2;
  }

  &.role-super-admin {
    background: #fce4ec;
    color: #c2185b;
  }

  &.role-user {
    background: #f3e5f5;
    color: #7b1fa2;
  }
}

/* Status Column */
.status-active {
  background: #e8f5e8;
  color: #2e7d32;

  mat-icon {
    color: #2e7d32;
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-xs);
  }
}

.status-inactive {
  background: #ffebee;
  color: #d32f2f;

  mat-icon {
    color: #d32f2f;
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-xs);
  }
}

/* Created Date */
.created-date {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Actions Column */
.actions-container {
  display: flex;
  gap: var(--spacing-xs);
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-sm);

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  &.view-btn {
    color: var(--accent-green);

    &:hover {
      background: rgba(76, 175, 80, 0.1);
    }
  }

  &.edit-btn {
    color: #ff9800;

    &:hover {
      background: rgba(255, 152, 0, 0.1);
    }
  }

  &.delete-btn {
    color: red;

    &:hover {
      background: rgba(244, 67, 54, 0.1);
    }
  }
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
}

.empty-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
}

.empty-state h3 {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0 0 var(--spacing-sm) 0;
}

.empty-state p {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

/* Pagination Styling */
.mat-mdc-paginator {
  background: var(--bg-card) !important;
  border-top: 1px solid var(--border-primary);
  color: var(--text-primary);
  font-size: 0.875rem;

  /* Pagination container */
  .mat-mdc-paginator-container {
    padding: var(--spacing-md) var(--spacing-lg);
    min-height: 56px;
    justify-content: space-between;
    align-items: center;
  }

  /* Range label styling */
  .mat-mdc-paginator-range-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0 var(--spacing-md);
  }

  /* Page size selector */
  .mat-mdc-paginator-page-size {
    align-items: center;

    .mat-mdc-paginator-page-size-label {
      color: var(--text-secondary);
      font-size: 0.875rem;
      margin-right: var(--spacing-sm);
    }

    .mat-mdc-select {
      color: var(--text-primary);
      font-size: 0.875rem;

      .mat-mdc-select-trigger {
        color: var(--text-primary);
        border: 1px solid var(--border-primary);
        border-radius: var(--radius-sm);
        padding: var(--spacing-xs) var(--spacing-sm);
        background: var(--bg-secondary);

        &:hover {
          background: var(--bg-hover);
          border-color: var(--border-secondary);
        }
      }

      .mat-mdc-select-arrow {
        color: var(--text-secondary);
      }
    }
  }

  /* Navigation buttons */
  .mat-mdc-paginator-navigation-previous,
  .mat-mdc-paginator-navigation-next,
  .mat-mdc-paginator-navigation-first,
  .mat-mdc-paginator-navigation-last {
    color: white !important;
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-sm);
    background: var(--bg-secondary);
    width: 36px;
    height: 36px;
    margin: 0 var(--spacing-xs);

    &:hover:not([disabled]) {
      background: var(--bg-hover);
      color: white !important;
      border-color: var(--border-secondary);
    }

    &[disabled] {
      color: var(--text-muted);
      background: var(--bg-tertiary);
      border-color: var(--border-primary);
      opacity: 0.6;
      cursor: not-allowed;
    }

    .mat-mdc-button-touch-target {
      width: 36px;
      height: 36px;
    }

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
      color: white !important;
    }
  }

  /* Actions container */
  .mat-mdc-paginator-range-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }
}

/* Pagination select dropdown styling */
.mat-mdc-select-panel {
  background: var(--bg-card) !important;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);

  .mat-mdc-option {
    color: var(--text-primary);
    font-size: 0.875rem;

    &:hover {
      background: var(--bg-hover) !important;
    }

    &.mat-mdc-option-active {
      background: var(--bg-secondary) !important;
      color: var(--text-primary);
    }

    &.mdc-list-item--selected {
      background: var(--accent-green) !important;
      color: white;
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-management-container {
    padding: var(--spacing-md);
  }

  .header-section {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .page-title {
    font-size: 1.5rem;
  }

  .table-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .table-header-left,
  .table-header-right {
    width: 100%;
  }

  .table-view-controls {
    justify-content: center;
  }

  .filters-container {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  :host ::ng-deep .search-field,
  :host ::ng-deep .filter-field {
    width: 100%;
  }

  .users-table {
    font-size: 0.75rem;
  }

  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .user-avatar,
  .user-avatar-placeholder {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
  }

  .actions-container {
    flex-direction: column;
    gap: 2px;
  }

  .action-btn {
    width: 32px;
    height: 32px;

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }

  /* Mobile pagination styling */
  .mat-mdc-paginator {
    .mat-mdc-paginator-container {
      padding: var(--spacing-sm) var(--spacing-md);
      min-height: 48px;
      flex-wrap: wrap;
      gap: var(--spacing-xs);
    }

    .mat-mdc-paginator-page-size {
      order: 1;
      width: 100%;
      justify-content: center;
      margin-bottom: var(--spacing-xs);

      .mat-mdc-paginator-page-size-label {
        font-size: 0.75rem;
      }
    }

    .mat-mdc-paginator-range-label {
      order: 2;
      font-size: 0.75rem;
      text-align: center;
      flex: 1;
    }

    .mat-mdc-paginator-range-actions {
      order: 3;
      justify-content: center;

      .mat-mdc-paginator-navigation-previous,
      .mat-mdc-paginator-navigation-next,
      .mat-mdc-paginator-navigation-first,
      .mat-mdc-paginator-navigation-last {
        width: 32px;
        height: 32px;
        margin: 0 2px;

        mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
          color: white !important;
        }
      }
    }
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .user-management-container {
    background: var(--bg-main-content); /* Light grayish-white background */
  }

  .table-card {
    background: #ffffff; /* White cards */
    border-color: #e0e0e0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .table-header {
    background: #ffffff;
    border-bottom-color: #e0e0e0;
  }

  .table-view-controls .view-btn {
    color: #666666;

    &.active {
      background: #f5f5f5;
      color: #333333;
    }

    &:hover:not(.active) {
      background: #f9f9f9;
      color: #333333;
    }
  }

  .reset-btn {
    color: #333333;
    background: #ffffff;
    border-color: #e0e0e0;

    &:hover {
      background: #f9f9f9;
      border-color: #d0d0d0;
    }
  }

  /* Form field styling for light theme */
  :host ::ng-deep .search-field,
  :host ::ng-deep .filter-field {
    .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #e0e0e0 !important;
      }
    }

    &.mat-focused .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #10b981 !important;
        border-width: 2px !important;
      }
    }

    &:hover:not(.mat-focused) .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #d0d0d0 !important;
      }
    }

    .mat-mdc-form-field-label {
      color: #666666 !important;
    }

    .mat-mdc-select-trigger,
    .mat-mdc-input-element {
      color: #333333 !important;
    }

    .mat-mdc-form-field-icon-suffix,
    .mat-mdc-select-arrow {
      color: #999999 !important;
    }
  }

  .page-title {
    color: #333333;
  }

  .page-subtitle {
    color: #666666;
  }

  .users-table {
    .mat-mdc-header-cell {
      background: #f5f5f5;
      color: #333333;
      border-bottom-color: #e0e0e0;
    }

    .mat-mdc-cell {
      border-bottom-color: #f0f0f0;
      color: #333333;
    }

    .mat-mdc-row:hover {
      background: #f9f9f9;
    }
  }

  .user-name {
    color: #333333;
  }

  .user-email {
    color: #666666;
  }

  .created-date {
    color: #666666;
  }

  .loading-text {
    color: #666666;
  }

  .empty-icon {
    color: #999999;
  }

  .empty-state h3 {
    color: #333333;
  }

  .empty-state p {
    color: #666666;
  }

  /* Pagination styling for light theme */
  .mat-mdc-paginator {
    background: #ffffff !important;
    border-top-color: #e0e0e0;
    color: #333333;

    .mat-mdc-paginator-range-label {
      color: #666666;
    }

    .mat-mdc-paginator-page-size-label {
      color: #666666;
    }

    .mat-mdc-select {
      color: #333333;

      .mat-mdc-select-trigger {
        color: #333333;
        border-color: #e0e0e0;
        background: #f5f5f5;

        &:hover {
          background: #f0f0f0;
          border-color: #d0d0d0;
        }
      }

      .mat-mdc-select-arrow {
        color: #666666;
      }
    }

    .mat-mdc-paginator-navigation-previous,
    .mat-mdc-paginator-navigation-next,
    .mat-mdc-paginator-navigation-first,
    .mat-mdc-paginator-navigation-last {
      color: white !important;
      border-color: #e0e0e0;
      background: #f5f5f5;

      &:hover:not([disabled]) {
        background: #f0f0f0;
        color: white !important;
        border-color: #d0d0d0;
      }

      &[disabled] {
        color: #999999;
        background: #f9f9f9;
        border-color: #e0e0e0;
      }
    }
  }

  /* Pagination select dropdown for light theme */
  .mat-mdc-select-panel {
    background: #ffffff !important;
    border-color: #e0e0e0;

    .mat-mdc-option {
      color: #333333;

      &:hover {
        background: #f0f0f0 !important;
      }

      &.mat-mdc-option-active {
        background: #f5f5f5 !important;
        color: #333333;
      }

      &.mdc-list-item--selected {
        background: #10b981 !important;
        color: white;
      }
    }
  }
}
