import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Subject, takeUntil } from 'rxjs';

import { UserManagementService } from '../../Services/UserManagement.service';
import {
  UserDetails,
  AddUserRequest,
  UpdateUserRequest,
  LoadingStates,
  ErrorStates,
} from '../../Models/UserManagement';

@Component({
  selector: 'app-add-edit-user',
  standalone: false,
  templateUrl: './add-edit-user.component.html',
  styleUrl: './add-edit-user.component.scss',
})
export class AddEditUserComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  userForm!: FormGroup;
  isEditMode = false;
  userId: string | null = null;
  pageTitle = 'Add User';

  // Available roles for selection
  availableRoles = [
    { value: 'user', label: 'User' },
    { value: 'admin', label: 'Admin' },
    { value: 'super admin', label: 'Super Admin' },
  ];

  // File upload
  selectedFile: File | null = null;
  profileImagePreview: string | null = null;

  // State management
  loadingStates: LoadingStates = {
    fetchingUsers: false,
    deletingUser: false,
    updatingUser: false,
    addingUser: false,
  };

  errorStates: ErrorStates = {
    fetchError: null,
    deleteError: null,
    updateError: null,
    addError: null,
  };

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private userManagementService: UserManagementService,
    private snackBar: MatSnackBar,
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.setupSubscriptions();
    this.checkRouteParams();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.userForm = this.fb.group(
      {
        fullName: ['', [Validators.required, Validators.minLength(2)]],
        email: ['', [Validators.required, Validators.email]],
        password: ['', [Validators.required, Validators.minLength(6)]],
        confirmPassword: ['', [Validators.required]],
        phoneNumber: [''],
        description: [''],
        isActive: [true],
        roles: [['user'], [Validators.required]],
      },
      { validators: this.passwordMatchValidator },
    );
  }

  private setupSubscriptions(): void {
    // Subscribe to loading states
    this.userManagementService.loadingStates$
      .pipe(takeUntil(this.destroy$))
      .subscribe((states) => {
        this.loadingStates = states;
      });

    // Subscribe to error states
    this.userManagementService.errorStates$
      .pipe(takeUntil(this.destroy$))
      .subscribe((states) => {
        this.errorStates = states;
        this.handleErrors(states);
      });
  }

  private checkRouteParams(): void {
    this.userId = this.route.snapshot.paramMap.get('id');
    if (this.userId) {
      this.isEditMode = true;
      this.pageTitle = 'Edit User';
      this.loadUserData(this.userId);
      // Remove password validation for edit mode
      this.userForm.get('password')?.clearValidators();
      this.userForm.get('confirmPassword')?.clearValidators();
      this.userForm.updateValueAndValidity();
    }
  }

  private loadUserData(userId: string): void {
    // TODO: Implement getUserById method in service
    // For now, we'll handle this when the service method is available
    console.log('Loading user data for ID:', userId);
  }

  private passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');

    if (
      password &&
      confirmPassword &&
      password.value !== confirmPassword.value
    ) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }

    return null;
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.selectedFile = file;

      // Create preview
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.profileImagePreview = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  }

  removeProfileImage(): void {
    this.selectedFile = null;
    this.profileImagePreview = null;
  }

  onSubmit(): void {
    if (this.userForm.valid) {
      if (this.isEditMode) {
        this.updateUser();
      } else {
        this.addUser();
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  private addUser(): void {
    const formValue = this.userForm.value;
    const addUserRequest: AddUserRequest = {
      email: formValue.email,
      fullName: formValue.fullName,
      password: formValue.password,
      isActive: formValue.isActive,
      phoneNumber: formValue.phoneNumber || undefined,
      description: formValue.description || undefined,
      profileImage: this.selectedFile || undefined,
    };

    this.userManagementService.addUser(addUserRequest).subscribe({
      next: () => {
        this.showSuccess('User created successfully');
        this.router.navigate(['/user-management/list']);
      },
      error: (error) => {
        console.error('Error creating user:', error);
      },
    });
  }

  private updateUser(): void {
    if (!this.userId) return;

    const formValue = this.userForm.value;
    const updateUserRequest: UpdateUserRequest = {
      id: this.userId,
      fullName: formValue.fullName,
      email: formValue.email,
      isActive: formValue.isActive,
      phoneNumber: formValue.phoneNumber || undefined,
      description: formValue.description || undefined,
      roles: formValue.roles,
    };

    this.userManagementService
      .updateUser(this.userId, updateUserRequest)
      .subscribe({
        next: () => {
          this.showSuccess('User updated successfully');
          this.router.navigate(['/user-management/list']);
        },
        error: (error) => {
          console.error('Error updating user:', error);
        },
      });
  }

  onCancel(): void {
    this.router.navigate(['/user-management/list']);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.userForm.controls).forEach((key) => {
      const control = this.userForm.get(key);
      control?.markAsTouched();
    });
  }

  private handleErrors(errorStates: ErrorStates): void {
    if (errorStates.addError) {
      this.showError(`Failed to create user: ${errorStates.addError}`);
    }
    if (errorStates.updateError) {
      this.showError(`Failed to update user: ${errorStates.updateError}`);
    }
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar'],
    });
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar'],
    });
  }

  // Getter methods for template
  get isLoading(): boolean {
    return this.loadingStates.addingUser || this.loadingStates.updatingUser;
  }

  get formTitle(): string {
    return this.isEditMode ? 'Edit User' : 'Add New User';
  }

  get submitButtonText(): string {
    if (this.isLoading) {
      return this.isEditMode ? 'Updating...' : 'Creating...';
    }
    return this.isEditMode ? 'Update User' : 'Create User';
  }
}
